{"name": "SERVOA_2V2_103C8", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [], "folders": [{"name": "MDK-ARM", "files": [{"path": "startup_stm32f103xb.s"}], "folders": []}, {"name": "User", "files": [], "folders": [{"name": "Core", "files": [{"path": "../Core/Src/canopen.c"}, {"path": "../Core/Src/crc_16.c"}, {"path": "../Core/Src/current_loop.c"}, {"path": "../Core/Src/delay.c"}, {"path": "../Core/Src/Dic.c"}, {"path": "../Core/Src/ds402.c"}, {"path": "../Core/Src/modbus.c"}, {"path": "../Core/Src/motion_control.c"}, {"path": "../Core/Src/ntc_calculate.c"}, {"path": "../Core/Src/parameter.c"}, {"path": "../Core/Src/mcpwm.c"}, {"path": "../Core/Src/position_loop.c"}, {"path": "../Core/Src/sin_table.c"}, {"path": "../Core/Src/sys.c"}, {"path": "../Core/Src/utils.c"}, {"path": "../Core/Src/velocity_loop.c"}, {"path": "../Core/Src/mt6825.c"}, {"path": "../Core/Src/main.c"}, {"path": "../Core/Src/gpio.c"}, {"path": "../Core/Src/adc.c"}, {"path": "../Core/Src/can.c"}, {"path": "../Core/Src/dma.c"}, {"path": "../Core/Src/spi.c"}, {"path": "../Core/Src/tim.c"}, {"path": "../Core/Src/usart.c"}, {"path": "../Core/Src/stm32f1xx_it.c"}, {"path": "../Core/Src/stm32f1xx_hal_msp.c"}, {"path": "../Core/Src/tamagawa.c"}, {"path": "../Core/Src/vofa_function.c"}], "folders": []}]}]}, {"name": "Drivers", "files": [], "folders": [{"name": "STM32F1xx_HAL_Driver", "files": [{"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_adc.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_adc_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_can.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_spi.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c"}, {"path": "../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../Core/Src/system_stm32f1xx.c"}], "folders": []}]}, {"name": "::CMSIS", "files": [], "folders": []}]}, "outDir": "build", "deviceName": "STM32F103C8", "packDir": ".pack/Keil/STM32F1xx_DFP.2.3.0", "miscInfo": {"uid": "7bcccecf6793aee13727d91c98f6285b"}, "targets": {"SERVOE_1Vx": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M3", "floatingPointHardware": "none", "useCustomScatterFile": false, "scatterFilePath": "", "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x5000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x10000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null", "archExtensions": ""}, "uploader": "OpenOCD", "uploadConfig": {"bin": "", "target": "stm32f1x", "interface": "cmsis-dap", "baseAddr": "0x08000000"}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "STLink": {"bin": "", "proType": "SWD", "resetMode": "default", "runAfterProgram": true, "speed": 4000, "address": "0x8000000", "elFile": "None", "optionBytes": ".eide/servoe_1vx.st.option.bytes.ini", "otherCmds": ""}, "OpenOCD": {"bin": "", "target": "stm32f1x", "interface": "cmsis-dap-v1", "baseAddr": "0x08000000"}}, "custom_dep": {"name": "default", "incList": [".", "../Core/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32F1xx/Include", "../Drivers/CMSIS/Include", ".cmsis/include", "RTE/_SERVOE_1Vx"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32F103xB"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "build bin task", "disable": false, "abortAfterFailed": true, "command": "\"C:\\\\Keil_v5\\\\ARM\\\\ARMCLANG\\\\bin\\\\fromelf.exe\" --bin -o \"${OutDir}\\\\APP_HAL.bin\" \"${OutDir}\\\\${ProjectName}.axf\""}], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "ro-base": "0x08000000", "rw-base": "0x20000000"}}}}, "SERVOA_2V2_103C8_FW": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M3", "floatingPointHardware": "none", "useCustomScatterFile": false, "scatterFilePath": "", "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x5000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8004000", "size": "0xb000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null", "archExtensions": ""}, "uploader": "OpenOCD", "uploadConfig": {"bin": "", "target": "stm32f1x", "interface": "cmsis-dap", "baseAddr": "0x08000000"}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "STLink": {"bin": "", "proType": "SWD", "resetMode": "default", "runAfterProgram": true, "speed": 4000, "address": "0x8000000", "elFile": "None", "optionBytes": ".eide/servoe_1vx.st.option.bytes.ini", "otherCmds": ""}, "OpenOCD": {"bin": "", "target": "stm32f1x", "interface": "cmsis-dap-v1", "baseAddr": "0x08000000"}}, "custom_dep": {"name": "default", "incList": [".", "../Core/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc", "../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32F1xx/Include", "../Drivers/CMSIS/Include", ".cmsis/include", "RTE/_SERVOA_2V2_103C8_FW"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32F103xB", "USER_VECT_TAB_ADDRESS"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "disable": true, "abortAfterFailed": false, "command": "mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${ProjectName}.axf\""}, {"name": "build bin task", "disable": false, "abortAfterFailed": true, "command": "\"C:\\\\Keil_v5\\\\ARM\\\\ARMCLANG\\\\bin\\\\fromelf.exe\" --bin -o \"${OutDir}\\\\APP_HAL.bin\" \"${OutDir}\\\\${ProjectName}.axf\""}], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000"}}}}}, "version": "3.6"}